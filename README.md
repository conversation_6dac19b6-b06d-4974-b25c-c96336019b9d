# Hono API with Supabase - Scalable REST API

Sebuah REST API yang scalable dan profesional dibangun dengan **Hono** yang ber<PERSON>lan di **Cloudflare Workers**, menggunakan **Supabase** sebagai database PostgreSQL, dan ditulis sepenuhnya dalam **TypeScript**.

## 🏗️ Arsitektur

Proyek ini mengimplementasikan **Separation of Concerns** yang ketat dengan struktur modular:

```
src/
├── common/                 # Shared utilities dan konfigurasi
│   └── supabase.ts        # Supabase client configuration
├── modules/               # Feature modules
│   └── products/          # Products module
│       ├── product.types.ts      # TypeScript interfaces & types
│       ├── product.repository.ts # Data access layer
│       ├── product.service.ts    # Business logic layer
│       └── product.routes.ts     # HTTP routes & controllers
└── index.ts              # Main application entry point
```

### Layer Architecture

1. **Routes Layer** (`*.routes.ts`) - HTTP endpoints dan request/response handling
2. **Service Layer** (`*.service.ts`) - Business logic, validasi, dan error handling
3. **Repository Layer** (`*.repository.ts`) - Data access dan database operations
4. **Types Layer** (`*.types.ts`) - TypeScript interfaces dan type definitions

## 🚀 Features

### Products CRUD API

- ✅ **GET** `/api/v1/products` - List products dengan pagination, filtering, dan sorting
- ✅ **GET** `/api/v1/products/:id` - Get product by ID
- ✅ **POST** `/api/v1/products` - Create new product
- ✅ **PUT** `/api/v1/products/:id` - Update product
- ✅ **DELETE** `/api/v1/products/:id` - Delete product
- ✅ **GET** `/api/v1/products/health` - Health check untuk products module

### Advanced Features

- 🔍 **Full-text search** pada nama dan deskripsi product
- 📄 **Pagination** dengan metadata lengkap
- 🔧 **Filtering** berdasarkan kategori, status aktif
- 📊 **Sorting** berdasarkan nama, harga, tanggal
- ✅ **Input validation** yang komprehensif
- 🛡️ **Error handling** yang konsisten
- 📝 **Request logging** dengan unique request ID
- 🌐 **CORS** support untuk frontend integration

## 🛠️ Tech Stack

- **Runtime**: Cloudflare Workers
- **Framework**: Hono.js
- **Database**: Supabase (PostgreSQL)
- **Language**: TypeScript
- **Package Manager**: npm

## 📋 Prerequisites

1. **Node.js** (v18 atau lebih baru)
2. **npm** atau **yarn**
3. **Cloudflare account** untuk deployment
4. **Supabase account** dan project

## ⚙️ Setup & Installation

### 1. Clone dan Install Dependencies

```bash
git clone <repository-url>
cd hono-api
npm install
```

### 2. Konfigurasi Environment Variables

Edit file `wrangler.jsonc` dan update environment variables:

```json
{
  "vars": {
    "SUPABASE_URL": "https://your-project.supabase.co",
    "SUPABASE_SERVICE_KEY": "your-service-role-key"
  }
}
```

⚠️ **Penting**: Gunakan **Service Role Key**, bukan **Anon Key** untuk server-side operations.

### 3. Setup Database

1. Buka Supabase Dashboard → SQL Editor
2. Jalankan script dari `database/schema.sql`
3. Verify bahwa tabel `products` sudah dibuat dengan sample data

### 4. Development

```bash
# Start development server
npm run dev

# API akan tersedia di http://localhost:8787
```

### 5. Deployment

```bash
# Deploy ke Cloudflare Workers
npm run deploy
```

## 📚 API Documentation

### Base URL

- **Development**: `http://localhost:8787`
- **Production**: `https://your-worker.your-subdomain.workers.dev`

### Authentication

Saat ini API tidak memerlukan authentication. Untuk production, pertimbangkan untuk menambahkan:

- API Keys
- JWT tokens
- Supabase Auth integration

### Response Format

Semua response menggunakan format yang konsisten:

**Success Response:**

```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

**Error Response:**

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": { ... }
  }
}
```

### Endpoints

#### 1. List Products

```http
GET /api/v1/products
```

**Query Parameters:**

- `page` (number): Halaman (default: 1)
- `limit` (number): Items per halaman (default: 10, max: 100)
- `category` (string): Filter berdasarkan kategori
- `is_active` (boolean): Filter berdasarkan status aktif
- `search` (string): Search dalam nama dan deskripsi
- `sort_by` (string): Field untuk sorting (`name`, `price`, `created_at`, `updated_at`)
- `sort_order` (string): Order sorting (`asc`, `desc`)

**Example:**

```bash
curl "http://localhost:8787/api/v1/products?page=1&limit=10&category=Electronics&search=MacBook"
```

#### 2. Get Product by ID

```http
GET /api/v1/products/:id
```

**Example:**

```bash
curl "http://localhost:8787/api/v1/products/123e4567-e89b-12d3-a456-426614174000"
```

#### 3. Create Product

```http
POST /api/v1/products
Content-Type: application/json
```

**Request Body:**

```json
{
  "name": "New Product",
  "description": "Product description",
  "price": 9999,
  "sku": "PROD-001",
  "stock_quantity": 100,
  "is_active": true,
  "image_url": "https://example.com/image.jpg",
  "category": "Electronics"
}
```

#### 4. Update Product

```http
PUT /api/v1/products/:id
Content-Type: application/json
```

**Request Body:** (semua field optional)

```json
{
  "name": "Updated Product Name",
  "price": 8999,
  "stock_quantity": 50
}
```

#### 5. Delete Product

```http
DELETE /api/v1/products/:id
```

## 🧪 Testing

### Manual Testing dengan curl

```bash
# Health check
curl http://localhost:8787/health

# List products
curl http://localhost:8787/api/v1/products

# Create product
curl -X POST http://localhost:8787/api/v1/products \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Product",
    "description": "A test product",
    "price": 1999,
    "sku": "TEST-001",
    "stock_quantity": 10,
    "category": "Test"
  }'
```

### Testing dengan Postman

Import collection dari `postman/` directory (jika tersedia) atau buat requests manual menggunakan endpoints di atas.

## 🔧 Development Guidelines

### Code Style

- Gunakan **TypeScript** untuk semua file
- Follow **ESLint** dan **Prettier** configurations
- Gunakan **explicit return types** untuk functions
- Implement **proper error handling**

### Adding New Modules

1. Buat folder baru di `src/modules/`
2. Implement 4 layer files: `*.types.ts`, `*.repository.ts`, `*.service.ts`, `*.routes.ts`
3. Register routes di `src/index.ts`
4. Update database schema jika diperlukan

### Database Migrations

Untuk perubahan schema database:

1. Buat file SQL baru di `database/migrations/`
2. Test di development environment
3. Apply ke production melalui Supabase Dashboard

## 🚀 Production Considerations

### Security

- [ ] Implement authentication (JWT/API Keys)
- [ ] Enable Row Level Security (RLS) di Supabase
- [ ] Add rate limiting
- [ ] Validate dan sanitize semua inputs
- [ ] Use HTTPS only

### Performance

- [ ] Implement caching strategy
- [ ] Add database connection pooling
- [ ] Monitor query performance
- [ ] Implement request/response compression

### Monitoring

- [ ] Add structured logging
- [ ] Implement health checks
- [ ] Set up error tracking (Sentry)
- [ ] Monitor API metrics

## 🤝 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

Jika mengalami issues atau memiliki pertanyaan:

1. Check existing [Issues](../../issues)
2. Create new issue dengan detail yang lengkap
3. Sertakan logs dan error messages

---

**Happy Coding! 🎉**
