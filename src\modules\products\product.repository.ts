import { SupabaseClient } from '@supabase/supabase-js';
import {
  Product,
  ProductRow,
  CreateProductRequest,
  UpdateProductRequest,
  GetProductsQuery,
  PaginatedResponse,
  ProductErrorCode,
} from './product.types.js';

/**
 * Repository class untuk menangani semua operasi database terkait products
 * Mengimplementasikan pattern Repository untuk memisahkan logika data access
 * dari business logic
 */
export class ProductRepository {
  private readonly tableName = 'products';

  constructor(private readonly supabase: SupabaseClient) {}

  /**
   * Mengambil semua products dengan pagination dan filtering
   * 
   * @param query - Parameter query untuk filtering dan pagination
   * @returns Promise dengan paginated response berisi products
   */
  async findMany(query: GetProductsQuery = {}): Promise<PaginatedResponse<Product>> {
    const {
      page = 1,
      limit = 10,
      category,
      is_active,
      search,
      sort_by = 'created_at',
      sort_order = 'desc',
    } = query;

    // Validasi pagination parameters
    const validatedLimit = Math.min(Math.max(limit, 1), 100);
    const validatedPage = Math.max(page, 1);
    const offset = (validatedPage - 1) * validatedLimit;

    // Build query dengan filtering
    let queryBuilder = this.supabase
      .from(this.tableName)
      .select('*', { count: 'exact' });

    // Apply filters
    if (category) {
      queryBuilder = queryBuilder.eq('category', category);
    }

    if (typeof is_active === 'boolean') {
      queryBuilder = queryBuilder.eq('is_active', is_active);
    }

    if (search) {
      queryBuilder = queryBuilder.or(
        `name.ilike.%${search}%,description.ilike.%${search}%`
      );
    }

    // Apply sorting
    queryBuilder = queryBuilder.order(sort_by, { ascending: sort_order === 'asc' });

    // Apply pagination
    queryBuilder = queryBuilder.range(offset, offset + validatedLimit - 1);

    const { data, error, count } = await queryBuilder;

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    const totalItems = count || 0;
    const totalPages = Math.ceil(totalItems / validatedLimit);

    return {
      data: (data as ProductRow[]).map(this.mapRowToProduct),
      pagination: {
        current_page: validatedPage,
        per_page: validatedLimit,
        total_items: totalItems,
        total_pages: totalPages,
        has_previous: validatedPage > 1,
        has_next: validatedPage < totalPages,
      },
    };
  }

  /**
   * Mengambil product berdasarkan ID
   * 
   * @param id - ID product yang dicari
   * @returns Promise dengan product atau null jika tidak ditemukan
   */
  async findById(id: string): Promise<Product | null> {
    const { data, error } = await this.supabase
      .from(this.tableName)
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null;
      }
      throw new Error(`Database error: ${error.message}`);
    }

    return this.mapRowToProduct(data as ProductRow);
  }

  /**
   * Mengambil product berdasarkan SKU
   * 
   * @param sku - SKU product yang dicari
   * @returns Promise dengan product atau null jika tidak ditemukan
   */
  async findBySku(sku: string): Promise<Product | null> {
    const { data, error } = await this.supabase
      .from(this.tableName)
      .select('*')
      .eq('sku', sku)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      throw new Error(`Database error: ${error.message}`);
    }

    return this.mapRowToProduct(data as ProductRow);
  }

  /**
   * Membuat product baru
   * 
   * @param productData - Data product yang akan dibuat
   * @returns Promise dengan product yang baru dibuat
   */
  async create(productData: CreateProductRequest): Promise<Product> {
    // Set default values
    const dataToInsert = {
      ...productData,
      stock_quantity: productData.stock_quantity ?? 0,
      is_active: productData.is_active ?? true,
    };

    const { data, error } = await this.supabase
      .from(this.tableName)
      .insert(dataToInsert)
      .select()
      .single();

    if (error) {
      // Handle unique constraint violation untuk SKU
      if (error.code === '23505' && error.message.includes('sku')) {
        throw new Error(`SKU '${productData.sku}' already exists`);
      }
      throw new Error(`Database error: ${error.message}`);
    }

    return this.mapRowToProduct(data as ProductRow);
  }

  /**
   * Update product berdasarkan ID
   * 
   * @param id - ID product yang akan diupdate
   * @param updateData - Data yang akan diupdate
   * @returns Promise dengan product yang sudah diupdate atau null jika tidak ditemukan
   */
  async update(id: string, updateData: UpdateProductRequest): Promise<Product | null> {
    const { data, error } = await this.supabase
      .from(this.tableName)
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      // Handle unique constraint violation untuk SKU
      if (error.code === '23505' && error.message.includes('sku')) {
        throw new Error(`SKU '${updateData.sku}' already exists`);
      }
      throw new Error(`Database error: ${error.message}`);
    }

    return this.mapRowToProduct(data as ProductRow);
  }

  /**
   * Menghapus product berdasarkan ID
   * 
   * @param id - ID product yang akan dihapus
   * @returns Promise dengan boolean indicating success
   */
  async delete(id: string): Promise<boolean> {
    const { error } = await this.supabase
      .from(this.tableName)
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    return true;
  }

  /**
   * Helper method untuk mapping database row ke Product interface
   * 
   * @param row - Raw data dari database
   * @returns Product object
   */
  private mapRowToProduct(row: ProductRow): Product {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      price: row.price,
      sku: row.sku,
      stock_quantity: row.stock_quantity,
      is_active: row.is_active,
      image_url: row.image_url,
      category: row.category,
      created_at: row.created_at,
      updated_at: row.updated_at,
    };
  }
}
