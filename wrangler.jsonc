{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "hono-api",
  "main": "src/index.ts",
  "compatibility_date": "2025-09-08",
  "compatibility_flags": ["nodejs_compat"],
  "vars": {
    "SUPABASE_URL": "https://lkwwwpcblpblfplburwi.supabase.co",
    "SUPABASE_SERVICE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxrd3d3cGNibHBibGZwbGJ1cndpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTcyOTUyODEsImV4cCI6MjA3Mjg3MTI4MX0.w6s9jsP5MOwgcIXwKpxbzJHRu-_KSwfdprKskdSieJ4"
  }
  // "kv_namespaces": [
  //   {
  //     "binding": "MY_KV_NAMESPACE",
  //     "id": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  //   }
  // ],
  // "r2_buckets": [
  //   {
  //     "binding": "MY_BUCKET",
  //     "bucket_name": "my-bucket"
  //   }
  // ],
  // "d1_databases": [
  //   {
  //     "binding": "MY_DB",
  //     "database_name": "my-database",
  //     "database_id": ""
  //   }
  // ],
  // "ai": {
  //   "binding": "AI"
  // },
  // "observability": {
  //   "enabled": true,
  //   "head_sampling_rate": 1
  // }
}
