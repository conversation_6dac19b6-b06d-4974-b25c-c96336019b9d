-- =====================================================
-- Products Table Schema for Supabase PostgreSQL
-- =====================================================
-- 
-- Jalankan script ini di Supabase SQL Editor untuk membuat
-- tabel products dengan semua constraints dan indexes yang diperlukan
--
-- Pastikan untuk menjalankan script ini sebelum menggunakan API

-- Enable UUID extension jika belum aktif
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop table jika sudah ada (hati-hati di production!)
-- DROP TABLE IF EXISTS products;

-- Buat tabel products
CREATE TABLE products (
    -- Primary key dengan UUID
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Informasi dasar product
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Harga dalam cents untuk menghindari floating point issues
    -- Contoh: $19.99 = 1999 cents
    price INTEGER NOT NULL CHECK (price >= 0),
    
    -- SKU harus unique untuk inventory management
    sku VARCHAR(100) NOT NULL UNIQUE,
    
    -- Stock quantity
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    
    -- Status aktif/non-aktif
    is_active BOOLEAN NOT NULL DEFAULT true,
    
    -- URL gambar product (optional)
    image_url TEXT,
    
    -- Kategori product
    category VARCHAR(100),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- Indexes untuk Performance Optimization
-- =====================================================

-- Index untuk pencarian berdasarkan SKU (sudah ada karena UNIQUE constraint)
-- CREATE INDEX idx_products_sku ON products(sku);

-- Index untuk filtering berdasarkan kategori
CREATE INDEX idx_products_category ON products(category);

-- Index untuk filtering berdasarkan status aktif
CREATE INDEX idx_products_is_active ON products(is_active);

-- Index untuk sorting berdasarkan nama
CREATE INDEX idx_products_name ON products(name);

-- Index untuk sorting berdasarkan harga
CREATE INDEX idx_products_price ON products(price);

-- Index untuk sorting berdasarkan created_at
CREATE INDEX idx_products_created_at ON products(created_at);

-- Composite index untuk filtering dan sorting yang sering digunakan
CREATE INDEX idx_products_active_category_created ON products(is_active, category, created_at DESC);

-- Index untuk full-text search pada nama dan deskripsi
CREATE INDEX idx_products_search ON products USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- =====================================================
-- Triggers untuk Auto-update Timestamps
-- =====================================================

-- Function untuk update timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger untuk auto-update updated_at
CREATE TRIGGER update_products_updated_at 
    BEFORE UPDATE ON products 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Row Level Security (RLS) - Optional
-- =====================================================
-- 
-- Uncomment jika ingin menggunakan RLS untuk security
-- Sesuaikan dengan kebutuhan authentication Anda

-- Enable RLS
-- ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Policy untuk read access (semua user bisa read)
-- CREATE POLICY "Allow read access for all users" ON products
--     FOR SELECT USING (true);

-- Policy untuk insert/update/delete (hanya authenticated users)
-- CREATE POLICY "Allow full access for authenticated users" ON products
--     FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- Sample Data untuk Testing
-- =====================================================

-- Insert beberapa sample products untuk testing
INSERT INTO products (name, description, price, sku, stock_quantity, category, image_url) VALUES
('MacBook Pro 14"', 'Apple MacBook Pro 14-inch with M2 Pro chip, 16GB RAM, 512GB SSD', 249900, 'MBP-14-M2-16-512', 10, 'Electronics', 'https://example.com/images/macbook-pro-14.jpg'),
('iPhone 15 Pro', 'Apple iPhone 15 Pro with 128GB storage, Titanium finish', 99900, 'IPH-15-PRO-128-TI', 25, 'Electronics', 'https://example.com/images/iphone-15-pro.jpg'),
('AirPods Pro 2nd Gen', 'Apple AirPods Pro with Active Noise Cancellation', 24900, 'APP-PRO-2ND', 50, 'Electronics', 'https://example.com/images/airpods-pro.jpg'),
('Organic Coffee Beans', 'Premium organic coffee beans from Colombia, 1kg', 2499, 'COFFEE-ORG-COL-1KG', 100, 'Food & Beverage', 'https://example.com/images/coffee-beans.jpg'),
('Wireless Mouse', 'Ergonomic wireless mouse with USB-C charging', 4999, 'MOUSE-WIRELESS-ERGO', 75, 'Electronics', 'https://example.com/images/wireless-mouse.jpg');

-- =====================================================
-- Verification Queries
-- =====================================================

-- Query untuk verify bahwa tabel sudah dibuat dengan benar
-- SELECT table_name, column_name, data_type, is_nullable, column_default
-- FROM information_schema.columns 
-- WHERE table_name = 'products' 
-- ORDER BY ordinal_position;

-- Query untuk verify indexes
-- SELECT indexname, indexdef 
-- FROM pg_indexes 
-- WHERE tablename = 'products';

-- Query untuk test sample data
-- SELECT id, name, price, sku, stock_quantity, category, created_at 
-- FROM products 
-- ORDER BY created_at DESC;
