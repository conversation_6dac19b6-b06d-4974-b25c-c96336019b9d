import { Hono } from 'hono';
import { getSupabaseClient, SupabaseEnv } from '../../common/supabase.js';
import { ProductRepository } from './product.repository.js';
import { ProductService } from './product.service.js';
import {
  CreateProductRequest,
  UpdateProductRequest,
  GetProductsQuery,
} from './product.types.js';

/**
 * Type untuk Cloudflare Workers environment variables
 * Extends SupabaseEnv untuk include semua env vars yang diperlukan
 */
type Env = SupabaseEnv;

/**
 * Product routes handler
 * Mengimplementasikan RESTful API endpoints untuk products:
 * - GET /products - List products dengan pagination dan filtering
 * - GET /products/:id - Get product by ID
 * - POST /products - Create new product
 * - PUT /products/:id - Update product
 * - DELETE /products/:id - Delete product
 */
export const productRoutes = new Hono<{ Bindings: Env }>();

/**
 * GET /products
 * Mengambil list products dengan support untuk:
 * - Pagination (page, limit)
 * - Filtering (category, is_active, search)
 * - Sorting (sort_by, sort_order)
 */
productRoutes.get('/', async (c) => {
  try {
    // Initialize dependencies
    const supabase = getSupabaseClient(c.env);
    const productRepository = new ProductRepository(supabase);
    const productService = new ProductService(productRepository);

    // Parse query parameters
    const query: GetProductsQuery = {
      page: c.req.query('page') ? parseInt(c.req.query('page')!) : undefined,
      limit: c.req.query('limit') ? parseInt(c.req.query('limit')!) : undefined,
      category: c.req.query('category') || undefined,
      is_active: c.req.query('is_active') ? c.req.query('is_active') === 'true' : undefined,
      search: c.req.query('search') || undefined,
      sort_by: c.req.query('sort_by') as any || undefined,
      sort_order: c.req.query('sort_order') as any || undefined,
    };

    // Call service
    const result = await productService.getProducts(query);

    // Return response dengan status code yang sesuai
    return c.json(result, result.success ? 200 : 400);
  } catch (error) {
    console.error('Error in GET /products:', error);
    return c.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    }, 500);
  }
});

/**
 * GET /products/:id
 * Mengambil product berdasarkan ID
 */
productRoutes.get('/:id', async (c) => {
  try {
    // Initialize dependencies
    const supabase = getSupabaseClient(c.env);
    const productRepository = new ProductRepository(supabase);
    const productService = new ProductService(productRepository);

    // Get ID dari path parameter
    const id = c.req.param('id');

    // Call service
    const result = await productService.getProductById(id);

    // Return response dengan status code yang sesuai
    const statusCode = result.success ? 200 : 
                      result.error.code === 'PRODUCT_NOT_FOUND' ? 404 : 400;
    
    return c.json(result, statusCode);
  } catch (error) {
    console.error('Error in GET /products/:id:', error);
    return c.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    }, 500);
  }
});

/**
 * POST /products
 * Membuat product baru
 */
productRoutes.post('/', async (c) => {
  try {
    // Initialize dependencies
    const supabase = getSupabaseClient(c.env);
    const productRepository = new ProductRepository(supabase);
    const productService = new ProductService(productRepository);

    // Parse request body
    let productData: CreateProductRequest;
    try {
      productData = await c.req.json();
    } catch (error) {
      return c.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid JSON in request body',
        },
      }, 400);
    }

    // Call service
    const result = await productService.createProduct(productData);

    // Return response dengan status code yang sesuai
    const statusCode = result.success ? 201 : 
                      result.error.code === 'DUPLICATE_SKU' ? 409 : 400;
    
    return c.json(result, statusCode);
  } catch (error) {
    console.error('Error in POST /products:', error);
    return c.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    }, 500);
  }
});

/**
 * PUT /products/:id
 * Update product berdasarkan ID
 */
productRoutes.put('/:id', async (c) => {
  try {
    // Initialize dependencies
    const supabase = getSupabaseClient(c.env);
    const productRepository = new ProductRepository(supabase);
    const productService = new ProductService(productRepository);

    // Get ID dari path parameter
    const id = c.req.param('id');

    // Parse request body
    let updateData: UpdateProductRequest;
    try {
      updateData = await c.req.json();
    } catch (error) {
      return c.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid JSON in request body',
        },
      }, 400);
    }

    // Call service
    const result = await productService.updateProduct(id, updateData);

    // Return response dengan status code yang sesuai
    const statusCode = result.success ? 200 : 
                      result.error.code === 'PRODUCT_NOT_FOUND' ? 404 :
                      result.error.code === 'DUPLICATE_SKU' ? 409 : 400;
    
    return c.json(result, statusCode);
  } catch (error) {
    console.error('Error in PUT /products/:id:', error);
    return c.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    }, 500);
  }
});

/**
 * DELETE /products/:id
 * Menghapus product berdasarkan ID
 */
productRoutes.delete('/:id', async (c) => {
  try {
    // Initialize dependencies
    const supabase = getSupabaseClient(c.env);
    const productRepository = new ProductRepository(supabase);
    const productService = new ProductService(productRepository);

    // Get ID dari path parameter
    const id = c.req.param('id');

    // Call service
    const result = await productService.deleteProduct(id);

    // Return response dengan status code yang sesuai
    const statusCode = result.success ? 200 : 
                      result.error.code === 'PRODUCT_NOT_FOUND' ? 404 : 400;
    
    return c.json(result, statusCode);
  } catch (error) {
    console.error('Error in DELETE /products/:id:', error);
    return c.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    }, 500);
  }
});

/**
 * Health check endpoint untuk products module
 * GET /products/health
 */
productRoutes.get('/health', async (c) => {
  try {
    // Test database connection
    const supabase = getSupabaseClient(c.env);
    
    // Simple query untuk test connection
    const { error } = await supabase
      .from('products')
      .select('count', { count: 'exact', head: true });

    if (error) {
      return c.json({
        success: false,
        message: 'Database connection failed',
        error: error.message,
      }, 503);
    }

    return c.json({
      success: true,
      message: 'Products module is healthy',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return c.json({
      success: false,
      message: 'Health check failed',
      error: error instanceof Error ? error.message : 'Unknown error',
    }, 503);
  }
});
