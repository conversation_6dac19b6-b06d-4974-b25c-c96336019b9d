/**
 * Core Product entity interface
 * Merepresentasikan struktur data product di database
 */
export interface Product {
  /** Unique identifier untuk product */
  id: string;
  
  /** Nama product */
  name: string;
  
  /** Deskripsi detail product */
  description: string | null;
  
  /** Harga product dalam cents (untuk menghindari floating point issues) */
  price: number;
  
  /** SKU (Stock Keeping Unit) - unique identifier untuk inventory */
  sku: string;
  
  /** Jumlah stock yang tersedia */
  stock_quantity: number;
  
  /** Status aktif/non-aktif product */
  is_active: boolean;
  
  /** URL gambar product (optional) */
  image_url: string | null;
  
  /** Kategori product */
  category: string | null;
  
  /** Timestamp ketika product dibuat */
  created_at: string;
  
  /** Timestamp ketika product terakhir diupdate */
  updated_at: string;
}

/**
 * Interface untuk membuat product baru
 * Menghilangkan field yang auto-generated (id, timestamps)
 */
export interface CreateProductRequest {
  /** Nama product (required) */
  name: string;
  
  /** Deskripsi product (optional) */
  description?: string;
  
  /** Harga product dalam cents (required) */
  price: number;
  
  /** SKU product (required, harus unique) */
  sku: string;
  
  /** Jumlah stock awal (default: 0) */
  stock_quantity?: number;
  
  /** Status aktif product (default: true) */
  is_active?: boolean;
  
  /** URL gambar product (optional) */
  image_url?: string;
  
  /** Kategori product (optional) */
  category?: string;
}

/**
 * Interface untuk update product
 * Semua field optional karena bisa partial update
 */
export interface UpdateProductRequest {
  /** Nama product */
  name?: string;
  
  /** Deskripsi product */
  description?: string;
  
  /** Harga product dalam cents */
  price?: number;
  
  /** SKU product */
  sku?: string;
  
  /** Jumlah stock */
  stock_quantity?: number;
  
  /** Status aktif product */
  is_active?: boolean;
  
  /** URL gambar product */
  image_url?: string;
  
  /** Kategori product */
  category?: string;
}

/**
 * Interface untuk query parameters saat mengambil list products
 */
export interface GetProductsQuery {
  /** Halaman untuk pagination (default: 1) */
  page?: number;
  
  /** Jumlah items per halaman (default: 10, max: 100) */
  limit?: number;
  
  /** Filter berdasarkan kategori */
  category?: string;
  
  /** Filter berdasarkan status aktif */
  is_active?: boolean;
  
  /** Search term untuk nama atau deskripsi */
  search?: string;
  
  /** Sort field (default: created_at) */
  sort_by?: 'name' | 'price' | 'created_at' | 'updated_at';
  
  /** Sort order (default: desc) */
  sort_order?: 'asc' | 'desc';
}

/**
 * Interface untuk response pagination
 */
export interface PaginatedResponse<T> {
  /** Array data hasil query */
  data: T[];
  
  /** Informasi pagination */
  pagination: {
    /** Halaman saat ini */
    current_page: number;
    
    /** Jumlah items per halaman */
    per_page: number;
    
    /** Total items di database */
    total_items: number;
    
    /** Total halaman */
    total_pages: number;
    
    /** Apakah ada halaman sebelumnya */
    has_previous: boolean;
    
    /** Apakah ada halaman selanjutnya */
    has_next: boolean;
  };
}

/**
 * Type untuk response API yang sukses
 */
export interface ApiSuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

/**
 * Type untuk response API yang error
 */
export interface ApiErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
}

/**
 * Union type untuk semua kemungkinan response API
 */
export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse;

/**
 * Enum untuk error codes yang mungkin terjadi
 */
export enum ProductErrorCode {
  NOT_FOUND = 'PRODUCT_NOT_FOUND',
  DUPLICATE_SKU = 'DUPLICATE_SKU',
  INVALID_PRICE = 'INVALID_PRICE',
  INVALID_STOCK = 'INVALID_STOCK',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  UNAUTHORIZED = 'UNAUTHORIZED',
}

/**
 * Interface untuk database row yang dikembalikan Supabase
 * Kadang Supabase mengembalikan struktur yang sedikit berbeda
 */
export interface ProductRow {
  id: string;
  name: string;
  description: string | null;
  price: number;
  sku: string;
  stock_quantity: number;
  is_active: boolean;
  image_url: string | null;
  category: string | null;
  created_at: string;
  updated_at: string;
}
