# Supabase Configuration
# Copy this file to .env and fill in your actual values

# Supabase Project URL
# You can find this in your Supabase project settings
SUPABASE_URL=https://your-project-id.supabase.co

# Supabase Service Role Key (NOT the anon key!)
# This is used for server-side operations
# You can find this in your Supabase project settings > API
SUPABASE_SERVICE_KEY=your-service-role-key-here

# Optional: Environment (development, staging, production)
NODE_ENV=development

# Optional: Port for local development
PORT=7000
