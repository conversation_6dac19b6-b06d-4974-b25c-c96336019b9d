import { createClient, SupabaseClient } from '@supabase/supabase-js';

/**
 * Interface untuk environment variables yang diperlukan oleh Supabase
 * Ini memastikan type safety untuk konfigurasi Node.js environment
 */
export interface SupabaseEnv {
  SUPABASE_URL: string;
  SUPABASE_SERVICE_KEY: string;
}

/**
 * Singleton instance untuk Supabase client
 * Menggunakan pattern singleton untuk memastikan hanya ada satu instance
 * client yang digunakan di seluruh aplikasi
 */
let supabaseInstance: SupabaseClient | null = null;

/**
 * Factory function untuk membuat atau mengambil instance Supabase client
 *
 * @param env - Environment variables (optional, akan menggunakan process.env jika tidak disediakan)
 * @returns SupabaseClient instance yang sudah dikonfigurasi
 *
 * @example
 * ```typescript
 * // Di dalam handler Hono
 * const supabase = getSupabaseClient();
 * const { data, error } = await supabase.from('products').select('*');
 * ```
 */
export function getSupabaseClient(env?: SupabaseEnv): SupabaseClient {
  // Gunakan process.env jika env tidak disediakan (untuk Node.js/Vercel)
  const supabaseUrl = env?.SUPABASE_URL || process.env.SUPABASE_URL;
  const supabaseKey = env?.SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_KEY;

  // Validasi environment variables
  if (!supabaseUrl || !supabaseKey) {
    throw new Error(
      'Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_KEY must be configured in .env file'
    );
  }

  // Implementasi singleton pattern
  // Jika instance sudah ada, return instance yang sama
  if (supabaseInstance) {
    return supabaseInstance;
  }

  // Buat instance baru dengan konfigurasi yang aman
  supabaseInstance = createClient(
    supabaseUrl,
    supabaseKey,
    {
      auth: {
        // Disable auto refresh untuk server-side usage
        autoRefreshToken: false,
        // Disable persist session untuk stateless environment
        persistSession: false,
      },
      // Konfigurasi untuk optimal performance di edge computing
      global: {
        headers: {
          'User-Agent': 'hono-api/1.0.0',
        },
      },
    }
  );

  return supabaseInstance;
}

/**
 * Helper function untuk reset Supabase instance
 * Berguna untuk testing atau ketika perlu reinitialize client
 */
export function resetSupabaseInstance(): void {
  supabaseInstance = null;
}

/**
 * Type guard untuk memvalidasi environment variables
 * 
 * @param env - Object yang akan divalidasi
 * @returns boolean - true jika env memiliki properties yang diperlukan
 */
export function isValidSupabaseEnv(env: any): env is SupabaseEnv {
  return (
    typeof env === 'object' &&
    env !== null &&
    typeof env.SUPABASE_URL === 'string' &&
    typeof env.SUPABASE_SERVICE_KEY === 'string' &&
    env.SUPABASE_URL.length > 0 &&
    env.SUPABASE_SERVICE_KEY.length > 0
  );
}
