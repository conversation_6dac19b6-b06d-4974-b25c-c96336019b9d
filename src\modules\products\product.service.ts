import { ProductRepository } from './product.repository.js';
import {
  Product,
  CreateProductRequest,
  UpdateProductRequest,
  GetProductsQuery,
  PaginatedResponse,
  ProductErrorCode,
  ApiResponse,
  ApiSuccessResponse,
  ApiErrorResponse,
} from './product.types.js';

/**
 * Service class untuk menangani business logic terkait products
 * Layer ini bertanggung jawab untuk:
 * - Validasi input
 * - Business rules enforcement
 * - Koordinasi dengan repository layer
 * - Error handling dan response formatting
 */
export class ProductService {
  constructor(private readonly productRepository: ProductRepository) {}

  /**
   * Mengambil list products dengan pagination dan filtering
   * 
   * @param query - Query parameters untuk filtering
   * @returns Promise dengan API response berisi paginated products
   */
  async getProducts(query: GetProductsQuery = {}): Promise<ApiResponse<PaginatedResponse<Product>>> {
    try {
      // Validasi query parameters
      const validatedQuery = this.validateGetProductsQuery(query);
      
      const result = await this.productRepository.findMany(validatedQuery);
      
      return {
        success: true,
        data: result,
        message: `Retrieved ${result.data.length} products`,
      };
    } catch (error) {
      return this.handleError(error, ProductErrorCode.DATABASE_ERROR);
    }
  }

  /**
   * Mengambil product berdasarkan ID
   * 
   * @param id - ID product yang dicari
   * @returns Promise dengan API response berisi product
   */
  async getProductById(id: string): Promise<ApiResponse<Product>> {
    try {
      // Validasi ID format
      if (!this.isValidUUID(id)) {
        return {
          success: false,
          error: {
            code: ProductErrorCode.VALIDATION_ERROR,
            message: 'Invalid product ID format',
          },
        };
      }

      const product = await this.productRepository.findById(id);
      
      if (!product) {
        return {
          success: false,
          error: {
            code: ProductErrorCode.NOT_FOUND,
            message: `Product with ID ${id} not found`,
          },
        };
      }

      return {
        success: true,
        data: product,
        message: 'Product retrieved successfully',
      };
    } catch (error) {
      return this.handleError(error, ProductErrorCode.DATABASE_ERROR);
    }
  }

  /**
   * Membuat product baru
   * 
   * @param productData - Data product yang akan dibuat
   * @returns Promise dengan API response berisi product yang baru dibuat
   */
  async createProduct(productData: CreateProductRequest): Promise<ApiResponse<Product>> {
    try {
      // Validasi input data
      const validationError = this.validateCreateProductRequest(productData);
      if (validationError) {
        return validationError;
      }

      // Check apakah SKU sudah ada
      const existingProduct = await this.productRepository.findBySku(productData.sku);
      if (existingProduct) {
        return {
          success: false,
          error: {
            code: ProductErrorCode.DUPLICATE_SKU,
            message: `Product with SKU '${productData.sku}' already exists`,
          },
        };
      }

      const newProduct = await this.productRepository.create(productData);
      
      return {
        success: true,
        data: newProduct,
        message: 'Product created successfully',
      };
    } catch (error) {
      return this.handleError(error, ProductErrorCode.DATABASE_ERROR);
    }
  }

  /**
   * Update product berdasarkan ID
   * 
   * @param id - ID product yang akan diupdate
   * @param updateData - Data yang akan diupdate
   * @returns Promise dengan API response berisi product yang sudah diupdate
   */
  async updateProduct(id: string, updateData: UpdateProductRequest): Promise<ApiResponse<Product>> {
    try {
      // Validasi ID format
      if (!this.isValidUUID(id)) {
        return {
          success: false,
          error: {
            code: ProductErrorCode.VALIDATION_ERROR,
            message: 'Invalid product ID format',
          },
        };
      }

      // Validasi update data
      const validationError = this.validateUpdateProductRequest(updateData);
      if (validationError) {
        return validationError;
      }

      // Check apakah SKU baru sudah ada (jika SKU diupdate)
      if (updateData.sku) {
        const existingProduct = await this.productRepository.findBySku(updateData.sku);
        if (existingProduct && existingProduct.id !== id) {
          return {
            success: false,
            error: {
              code: ProductErrorCode.DUPLICATE_SKU,
              message: `Product with SKU '${updateData.sku}' already exists`,
            },
          };
        }
      }

      const updatedProduct = await this.productRepository.update(id, updateData);
      
      if (!updatedProduct) {
        return {
          success: false,
          error: {
            code: ProductErrorCode.NOT_FOUND,
            message: `Product with ID ${id} not found`,
          },
        };
      }

      return {
        success: true,
        data: updatedProduct,
        message: 'Product updated successfully',
      };
    } catch (error) {
      return this.handleError(error, ProductErrorCode.DATABASE_ERROR);
    }
  }

  /**
   * Menghapus product berdasarkan ID
   * 
   * @param id - ID product yang akan dihapus
   * @returns Promise dengan API response
   */
  async deleteProduct(id: string): Promise<ApiResponse<{ deleted: boolean }>> {
    try {
      // Validasi ID format
      if (!this.isValidUUID(id)) {
        return {
          success: false,
          error: {
            code: ProductErrorCode.VALIDATION_ERROR,
            message: 'Invalid product ID format',
          },
        };
      }

      // Check apakah product exists
      const existingProduct = await this.productRepository.findById(id);
      if (!existingProduct) {
        return {
          success: false,
          error: {
            code: ProductErrorCode.NOT_FOUND,
            message: `Product with ID ${id} not found`,
          },
        };
      }

      await this.productRepository.delete(id);
      
      return {
        success: true,
        data: { deleted: true },
        message: 'Product deleted successfully',
      };
    } catch (error) {
      return this.handleError(error, ProductErrorCode.DATABASE_ERROR);
    }
  }

  /**
   * Validasi query parameters untuk getProducts
   */
  private validateGetProductsQuery(query: GetProductsQuery): GetProductsQuery {
    const validated: GetProductsQuery = { ...query };

    // Validasi dan normalize pagination
    if (validated.page && validated.page < 1) {
      validated.page = 1;
    }
    if (validated.limit && (validated.limit < 1 || validated.limit > 100)) {
      validated.limit = 10;
    }

    // Validasi sort parameters
    const validSortFields = ['name', 'price', 'created_at', 'updated_at'];
    if (validated.sort_by && !validSortFields.includes(validated.sort_by)) {
      validated.sort_by = 'created_at';
    }

    const validSortOrders = ['asc', 'desc'];
    if (validated.sort_order && !validSortOrders.includes(validated.sort_order)) {
      validated.sort_order = 'desc';
    }

    return validated;
  }

  /**
   * Validasi data untuk create product
   */
  private validateCreateProductRequest(data: CreateProductRequest): ApiErrorResponse | null {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Product name is required');
    }

    if (!data.sku || data.sku.trim().length === 0) {
      errors.push('Product SKU is required');
    }

    if (typeof data.price !== 'number' || data.price < 0) {
      errors.push('Product price must be a non-negative number');
    }

    if (data.stock_quantity !== undefined && (typeof data.stock_quantity !== 'number' || data.stock_quantity < 0)) {
      errors.push('Stock quantity must be a non-negative number');
    }

    if (errors.length > 0) {
      return {
        success: false,
        error: {
          code: ProductErrorCode.VALIDATION_ERROR,
          message: 'Validation failed',
          details: errors,
        },
      };
    }

    return null;
  }

  /**
   * Validasi data untuk update product
   */
  private validateUpdateProductRequest(data: UpdateProductRequest): ApiErrorResponse | null {
    const errors: string[] = [];

    if (data.name !== undefined && (!data.name || data.name.trim().length === 0)) {
      errors.push('Product name cannot be empty');
    }

    if (data.sku !== undefined && (!data.sku || data.sku.trim().length === 0)) {
      errors.push('Product SKU cannot be empty');
    }

    if (data.price !== undefined && (typeof data.price !== 'number' || data.price < 0)) {
      errors.push('Product price must be a non-negative number');
    }

    if (data.stock_quantity !== undefined && (typeof data.stock_quantity !== 'number' || data.stock_quantity < 0)) {
      errors.push('Stock quantity must be a non-negative number');
    }

    if (errors.length > 0) {
      return {
        success: false,
        error: {
          code: ProductErrorCode.VALIDATION_ERROR,
          message: 'Validation failed',
          details: errors,
        },
      };
    }

    return null;
  }

  /**
   * Validasi UUID format
   */
  private isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Generic error handler
   */
  private handleError(error: any, defaultCode: ProductErrorCode): ApiErrorResponse {
    console.error('ProductService error:', error);
    
    return {
      success: false,
      error: {
        code: defaultCode,
        message: error.message || 'An unexpected error occurred',
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      },
    };
  }
}
