import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { productRoutes } from './modules/products/product.routes.js';
import { SupabaseEnv, isValidSupabaseEnv } from './common/supabase.js';

/**
 * Type untuk Cloudflare Workers environment variables
 */
type Env = SupabaseEnv;

/**
 * Type untuk context variables
 */
type Variables = {
  requestId: string;
};

/**
 * Main Hono application instance
 * Mengkonfigurasi middleware dan routes untuk REST API
 */
const app = new Hono<{ Bindings: Env; Variables: Variables }>();

/**
 * Global middleware setup
 */

// CORS middleware - mengizinkan cross-origin requests
app.use('*', cors({
  origin: ['http://localhost:3000', 'https://yourdomain.com'], // Sesuaikan dengan domain frontend Anda
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  exposeHeaders: ['Content-Length', 'X-Request-ID'],
  maxAge: 86400, // 24 hours
  credentials: true,
}));

// Logger middleware - untuk logging requests
app.use('*', logger());

// Pretty JSON middleware - untuk format JSON response yang rapi
app.use('*', prettyJSON());

// Request ID middleware - menambahkan unique ID untuk setiap request
app.use('*', async (c, next) => {
  const requestId = crypto.randomUUID();
  c.set('requestId', requestId);
  c.header('X-Request-ID', requestId);
  await next();
});

// Environment validation middleware
app.use('*', async (c, next) => {
  // Skip validation untuk health check endpoints
  if (c.req.path === '/' || c.req.path === '/health') {
    await next();
    return;
  }

  // Validasi environment variables
  if (!isValidSupabaseEnv(c.env)) {
    return c.json({
      success: false,
      error: {
        code: 'CONFIGURATION_ERROR',
        message: 'Missing or invalid environment configuration. Please check SUPABASE_URL and SUPABASE_SERVICE_KEY.',
      },
    }, 500);
  }

  await next();
});

/**
 * Routes setup
 */

// Root endpoint - API information
app.get('/', (c) => {
  return c.json({
    success: true,
    message: 'Hono API with Supabase - Products CRUD',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      products: '/api/v1/products',
      health: '/health',
    },
    documentation: 'https://github.com/yourusername/hono-api', // Sesuaikan dengan repo Anda
  });
});

// Health check endpoint
app.get('/health', (c) => {
  return c.json({
    success: true,
    message: 'API is healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime ? process.uptime() : 'N/A',
  });
});

// Mount product routes dengan prefix
app.route('/api/v1/products', productRoutes);

/**
 * Global error handler
 */
app.onError((err, c) => {
  console.error('Global error handler:', err);

  return c.json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred',
      requestId: c.get('requestId'),
    },
  }, 500);
});

/**
 * 404 handler untuk routes yang tidak ditemukan
 */
app.notFound((c) => {
  return c.json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${c.req.method} ${c.req.path} not found`,
      requestId: c.get('requestId'),
    },
  }, 404);
});

export default app;
